<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/background_primary"
    tools:context=".ui.client.ClientFragment">

    <!-- Número de Documento -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/textView3"
            style="@style/Text.Body"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.4"
            android:text="Nro. Documento:"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editNroDocumento"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:ems="10"
            android:inputType="text"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_hint"/>
    </LinearLayout>

    <!-- Nombre del Cliente -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/textNombreClient"
            style="@style/Text.Body"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.4"
            android:text="Nombre Cliente:"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editNombreClient"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:ems="20"
            android:inputType="textCapWords|textMultiLine"
            android:maxLength="100"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_hint"/>
    </LinearLayout>

    <!-- Email -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="24dp"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/textEmail"
            style="@style/Text.Body"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.4"
            android:text="e-Mail:"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/editEmail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.6"
            android:ems="10"
            android:inputType="textEmailAddress"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_hint"/>
    </LinearLayout>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBarCliente"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="24dp"
        android:indeterminateTint="@color/primary_blue_600"
        android:visibility="gone"/>

    <!-- Botones -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="8dp">

        <Button
            android:id="@+id/btnGuardar"
            style="@style/Button.Success"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Guardar"
            android:textAllCaps="false"/>

        <Button
            android:id="@+id/btnCancelar"
            style="@style/Button.Secondary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Cancelar"
            android:textAllCaps="false"/>
    </LinearLayout>

</LinearLayout>