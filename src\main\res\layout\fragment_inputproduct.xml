<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:padding="16dp"
    tools:context=".ui.inputproduct.InputProductFragment">

    <!-- Título -->
    <TextView
        android:id="@+id/text_inputProduct"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Ingrese el código de producto"
        android:textAlignment="center"
        android:textSize="18sp"
        android:textStyle="bold"
        app:tabTextColor="@color/text_on_primary"
        app:tabSelectedTextColor="@color/text_on_primary"
        app:tabIndicatorColor="@color/text_on_primary"
        app:tabRippleColor="@color/primary_blue_200"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Input + Botón Escanear -->
    <LinearLayout
        android:id="@+id/inputContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:layout_marginTop="10dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/text_inputProduct">

        <EditText
            android:id="@+id/editTextProductCode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Código de producto"
            android:textSize="16sp"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"

            />



        <Button
            android:id="@+id/buttonAceptar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="Buscar"
            android:backgroundTint="@color/primary_blue_600"
            android:textColor="@color/white"
            android:padding="12dp"
            android:textAllCaps="false"
            style="@style/Widget.MaterialComponents.Button"
             />


    </LinearLayout>

    <!-- Botones principales -->
    <Button
        android:id="@+id/buttonScanBarcode"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:text="Escanear"
        android:backgroundTint="@color/primary_blue_600"
        android:textColor="@color/white"
        android:padding="2dp"
        app:layout_constraintEnd_toStartOf="@+id/guideline"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/inputContainer"/>

    <Button
        android:id="@+id/btnShowCart"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginTop="10dp"
        android:text="Carrito"
        android:backgroundTint="@color/alt1_secondary_blue_500"
        android:textColor="@color/white"
        android:padding="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/guideline2"
        app:layout_constraintTop_toBottomOf="@id/inputContainer" />

    <!-- Información del producto -->
    <TextView
        android:id="@+id/textDescripcion"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textColor="@color/black"
        android:textSize="14sp"
        android:textStyle="bold"
        android:padding="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/buttonScanBarcode" />

    <TextView
        android:id="@+id/textPrecio"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="@color/black"
        android:textSize="14sp"
        android:textStyle="bold"
        android:padding="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textDescripcion" />

    <!-- Cantidad -->
    <TextView
        android:id="@+id/textView2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Cantidad:"
        app:tabTextColor="@color/text_on_primary"
        app:tabSelectedTextColor="@color/text_on_primary"
        app:tabIndicatorColor="@color/text_on_primary"
        app:tabRippleColor="@color/primary_blue_200"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textPrecio" />

    <!-- Tallas -->
    <Spinner
        android:id="@+id/numberSpinner"
        android:layout_width="80dp"
        android:layout_height="40dp"
        android:layout_marginStart="12dp"
        android:background="@drawable/spinner_background"
        android:spinnerMode="dropdown"
        android:textSize="12sp"
        app:tabTextColor="@color/text_on_primary"
        app:tabSelectedTextColor="@color/text_on_primary"
        app:tabIndicatorColor="@color/text_on_primary"
        app:tabRippleColor="@color/primary_blue_200"
        app:layout_constraintBottom_toBottomOf="@id/textView2"
        app:layout_constraintStart_toEndOf="@id/textView2"
        app:layout_constraintTop_toTopOf="@id/textView2"
        app:layout_constraintVertical_bias="0.375" />

    <TextView
        android:id="@+id/textViewTalla"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:text="Talla:"
        app:tabTextColor="@color/text_on_primary"
        app:tabSelectedTextColor="@color/text_on_primary"
        app:tabIndicatorColor="@color/text_on_primary"
        app:tabRippleColor="@color/primary_blue_200"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textView2" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewSizes"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:padding="5dp"
        android:clipToPadding="false"
        android:scrollbars="vertical"
        app:tabTextColor="@color/text_on_primary"
        app:tabSelectedTextColor="@color/text_on_primary"
        app:tabIndicatorColor="@color/text_on_primary"
        app:tabRippleColor="@color/primary_blue_200"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/textViewTalla" />


    <Button
        android:id="@+id/buttonAddProduct"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:backgroundTint="@color/alt4_primary_navy_900"
        android:padding="12dp"
        android:text="Agregar"
        app:tabTextColor="@color/text_on_primary"
        app:tabSelectedTextColor="@color/text_on_primary"
        app:tabIndicatorColor="@color/text_on_primary"
        app:tabRippleColor="@color/primary_blue_200"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.497"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/recyclerViewSizes" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.33" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.66" />



    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBarInput"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateTint="@color/purple_500"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>