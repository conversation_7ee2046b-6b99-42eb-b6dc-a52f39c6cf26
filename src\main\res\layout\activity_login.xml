<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/background_primary"
    tools:context="ui.login.LoginActivity">

    <!-- Contenedor principal del formulario con peso flexible -->
    <LinearLayout
        android:id="@+id/login_form"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="visible">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="24dp"
            android:src="@drawable/logosalesmovile"
            android:scaleType="centerCrop"
            android:contentDescription="@string/salesmobile" />

        <TextView
            style="@style/Text.Title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/aplicaci_n_de_ventas"
            android:textSize="20sp"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="32dp"
            android:textStyle="bold"
            android:layout_gravity="center_horizontal"/>

        <!-- Usuario SQL Server -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etUsuarioSQL"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/c_digo_de_vendedor"
                android:inputType="text"
                android:textColor="@color/text_primary"
                android:maxLines="1"/>
        </com.google.android.material.textfield.TextInputLayout>

        <!-- Password SQL Server -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etPasswordSQL"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Contraseña Vendedor"
                android:inputType="textPassword"
                android:textColor="@color/text_primary"
                android:maxLines="1"/>
        </com.google.android.material.textfield.TextInputLayout>

        <CheckBox
            android:id="@+id/cbRecordar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Recordar contraseña"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/primary_blue_600"
            android:layout_marginBottom="16dp"
            tools:ignore="HardcodedText" />

        <Button
            android:id="@+id/btnLogin"
            style="@style/Button.Primary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/conectar"
            android:layout_marginBottom="16dp"/>

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:indeterminateTint="@color/primary_blue_600"
            android:visibility="gone"/>
    </LinearLayout>

    <!-- Contenedor para el fragmento de configuración -->
    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:visibility="gone"/>

    <!-- Botón de Configuración siempre visible en la parte inferior -->
    <Button
        android:id="@+id/btnConfiguracion"
        style="@style/Button.Secondary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Configurar Conexión"
        tools:ignore="HardcodedText,VisualLintButtonSize" />

</LinearLayout>