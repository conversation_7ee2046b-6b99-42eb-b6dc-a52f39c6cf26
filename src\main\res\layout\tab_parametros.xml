<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Parámetros del Sistema"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"
            android:textColor="?android:attr/textColorPrimary"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary"
            />
        <TextView
            android:id="@+id/tvTest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Test PC:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvTestPc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Entorno:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvEntorno"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Dev Bancard URL:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvDevBancardUrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Prod Bancard URL:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvProdBancardUrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Dev Default URL:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvDevDefaultUrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Prod Default URL:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvProdDefaultUrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Bancard Commerce Code:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvBancardCommerceCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Bancard Commerce Branch:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvBancardCommerceBranch"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="App Movil Default URL:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvAppmovilDefaultUrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

    </LinearLayout>
</ScrollView>
