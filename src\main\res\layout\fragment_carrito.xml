<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fragment_carrito"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:padding="16dp"
    tools:context=".ui.inputproduct.CarritoFragment">

    <!-- Encabezado -->
    <TextView
        android:id="@+id/titleCart"
        style="@style/Text.Title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Carrito de Compras"
        android:textColor="@color/text_primary"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Lista de productos -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/gridViewProducts"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="60dp"
        android:clipToPadding="false"
        android:paddingBottom="80dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@+id/bottomButtons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleCart" />

    <!-- Total -->

    <!-- Botones inferiores -->
    <TextView
        android:id="@+id/totalText"
        style="@style/Text.Subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="96dp"
        android:layout_marginBottom="10dp"
        android:text="Total:"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/totalAmount"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/totalAmount"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/totalAmount"
        style="@style/Text.Subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10sp"
        android:text="$0.00"
        android:textColor="@color/primary_blue_700"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/bottomButtons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.093"
        app:layout_constraintStart_toEndOf="@id/totalText"
        app:layout_constraintTop_toBottomOf="@id/gridViewProducts"
        app:layout_constraintVertical_bias="0.541" />

    <LinearLayout
        android:id="@+id/bottomButtons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <Button
            android:id="@+id/btnCloseCart"
            style="@style/Button.Secondary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="40dp"
            android:text="Volver"
            android:textAllCaps="false"/>

        <Button
            android:id="@+id/btnCobrar"
            style="@style/Button.Primary"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="40dp"
            android:text="Cobrar"
            android:textAllCaps="false"/>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>