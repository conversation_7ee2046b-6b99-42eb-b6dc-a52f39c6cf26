<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:padding="10dp"
    tools:context=".ui.home.HomeFragment">

    <!-- ========== HEADER: Nombre de la sucursal con nuevo diseño ========== -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_sucursal"
        style="@style/Card.Elevated"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        app:cardBackgroundColor="@color/primary_blue_700"
        app:cardCornerRadius="16dp"
        app:cardElevation="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="24dp"
            android:gravity="center_vertical">

            <!-- Icono de sucursal -->
            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_business"
                app:tint="@color/text_on_primary"
                android:layout_marginEnd="16dp"
                android:contentDescription="Icono sucursal" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Establecimiento"
                    android:textColor="@color/text_on_primary"
                    android:textSize="12sp"
                    android:alpha="0.8" />

                <TextView
                    android:id="@+id/tv_nombre_sucursal"
                    style="@style/Text.Subtitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Nombre Establecimiento"
                    android:textColor="@color/text_on_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginTop="4dp" />
            </LinearLayout>


        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>
    <!-- ========== COTIZACIÓN: Card con gradiente visual ========== -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_cotizacion"
        style="@style/Card.Elevated"
        android:layout_width="0dp"
        android:layout_height="120dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        app:cardBackgroundColor="@color/info"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_sucursal">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="5dp"
            android:gravity="center_vertical">

            <!-- Icono con fondo circular -->
            <FrameLayout
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/info_light"
                android:layout_marginEnd="20dp">

                <ImageView
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_attach_money"
                    app:tint="@color/info_dark"
                    android:contentDescription="Icono cotización" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    style="@style/Text.Caption"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cotización USD/PYG"
                    android:textColor="@color/text_on_primary"
                    android:textSize="13sp"
                    android:alpha="0.85" />

                <TextView
                    android:id="@+id/tv_cotizacion"
                    style="@style/Text.Title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Gs. 7.350.00"
                    android:textColor="@color/text_on_primary"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:layout_marginTop="6dp" />


            </LinearLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- ========== VENTAS PRINCIPALES: Card destacada con diseño moderno ========== -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_main"
        style="@style/Card.Elevated"
        android:layout_width="0dp"
        android:layout_height="120dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="20dp"
        app:cardBackgroundColor="@color/success"
        app:cardCornerRadius="20dp"
        app:cardElevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_cotizacion">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="5dp">

            <!-- Header con icono decorativo -->
            <FrameLayout
                android:id="@+id/frame_icon_main"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/success_light"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_trending_up"
                    app:tint="@color/success_dark"
                    android:contentDescription="Icono ventas" />
            </FrameLayout>

            <TextView
                android:id="@+id/tv_titulo"
                style="@style/Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.85"
                android:text="Ventas del día"
                android:textColor="@color/text_on_secondary"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_fecha"
                style="@style/Text.Subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:text="Hoy, 01 Agosto"
                android:textColor="@color/text_on_secondary"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_titulo" />

            <!-- Monto principal destacado -->

            <TextView
                android:id="@+id/tv_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:fontFamily="sans-serif-medium"
                android:text="Gs. 2,450,000"
                android:textColor="@color/text_on_secondary"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_fecha" />



        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- ========== GRID DE MÉTRICAS: Diseño en dos columnas ========== -->
    <!-- Tarjeta de Ticket medio con diseño moderno -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_ticket"
        style="@style/Card.Elevated"
        android:layout_width="0dp"
        android:layout_height="120dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="10dp"
        android:layout_marginBottom="20dp"
        app:cardBackgroundColor="@color/warning"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toStartOf="@+id/card_transacciones"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_main"
        app:layout_constraintWidth_percent="0.48">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="5dp">

            <!-- Icono con fondo -->
            <FrameLayout
                android:id="@+id/frame_icon_ticket"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/warning_light"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/ic_ticket"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_average"
                    app:tint="@color/warning_dark"
                    android:contentDescription="Icono promedio" />
            </FrameLayout>

            <TextView
                android:id="@+id/tv_label_ticket"
                style="@style/Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginTop="40dp"
                android:alpha="0.8"
                android:text="Venta promedio"
                android:textColor="@color/text_on_primary"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_ticket_medio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginTop="4dp"
                android:fontFamily="sans-serif-medium"
                android:text="Gs. 10.000.000"
                android:textColor="@color/text_on_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_label_ticket" />

            <!-- Indicador de cambio -->
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Tarjeta de Transacciones con diseño complementario -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_transacciones"
        style="@style/Card.Elevated"
        android:layout_width="0dp"
        android:layout_height="120dp"
        android:layout_marginTop="20dp"
        android:layout_marginStart="0dp"
        android:layout_marginBottom="20dp"

        app:cardBackgroundColor="@color/primary_blue_600"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/card_ticket"
        app:layout_constraintTop_toBottomOf="@+id/card_main"
        app:layout_constraintWidth_percent="0.48">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="5dp">

            <!-- Icono con fondo -->
            <FrameLayout
                android:id="@+id/frame_icon_trans"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/circle_shape"
                android:backgroundTint="@color/primary_blue_200"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/ic_transacciones"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:src="@drawable/ic_receipt"
                    app:tint="@color/primary_blue_800"
                    android:contentDescription="Icono transacciones" />
            </FrameLayout>

            <TextView
                android:id="@+id/tv_label_transacciones"
                style="@style/Text.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cant. Ventas"
                android:textColor="@color/text_on_primary"
                android:textSize="12sp"
                android:alpha="0.85"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_transacciones"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="40dp"
                android:layout_marginTop="12dp"
                android:fontFamily="sans-serif-medium"
                android:text="10"
                android:textColor="@color/text_on_primary"
                android:textSize="28sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_label_transacciones" />


        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- ========== ELEMENTOS DE ESTADO Y FOOTER ========== -->
    <!-- ProgressBar con colores actualizados -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:indeterminateTint="@color/primary_blue_600"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Texto de versión con diseño mejorado -->
    <TextView
        android:id="@+id/tv_version"
        style="@style/Text.Caption"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Versión 1.3.0"
        android:textColor="@color/text_hint"
        android:textSize="11sp"
        android:alpha="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="20dp"/>

</androidx.constraintlayout.widget.ConstraintLayout>