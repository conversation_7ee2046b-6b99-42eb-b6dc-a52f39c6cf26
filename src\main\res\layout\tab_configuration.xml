<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Configuración del Sistema"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp"
            android:textColor="?android:attr/textColorPrimary"
            />

        <!-- Configuración de Conexión -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="CONFIGURACIÓN DE CONEXIÓN"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            app:backgroundColor="@color/primary_blue_500"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Servidor:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigServidor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Base de Datos:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigBaseDatos"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Punto de Venta:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigPuntoVenta"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <!-- Información del Punto de Venta -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="INFORMACIÓN DEL PUNTO DE VENTA"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Nombre Punto de Venta:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigNombrePuntoVenta"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Prefijo Punto de Venta:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigPrefijoPuntoVenta"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp" />

        <!-- Información del Vendedor -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="INFORMACIÓN DEL VENDEDOR"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Usuario Vendedor:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigCodigoVendedor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp" />

        <!-- Cotización -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="COTIZACIÓN"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Valor Cotización:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigCotizacion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />
        <!-- Configuración de APIs -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="CONFIGURACIÓN DE APIS"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Código Empresa:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigCodEmpresa"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Código Sucursal:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigCodSucursal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="URL API:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigUrlApi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Usuario API:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigUsuarioApi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Clave API:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigClaveApi"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <!-- API Secundaria -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="CONFIGURACIÓN API SECUNDARIA"
            android:textSize="16sp"
            android:textStyle="bold"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="URL API 2:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigUrlApi2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Usuario API 2:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigUsuarioApi2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Clave API 2:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigClaveApi2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Código Empresa 2:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigCodEmpresa2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Código Sucursal 2:"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary" />
        <TextView
            android:id="@+id/tvConfigCodSucursal2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:drawable/edit_text"
            android:padding="8dp"
            android:layout_marginBottom="8dp"
            app:tabTextColor="@color/text_on_primary"
            app:tabSelectedTextColor="@color/text_on_primary"
            app:tabIndicatorColor="@color/text_on_primary"
            app:tabRippleColor="@color/primary_blue_200"
            />





    </LinearLayout>
</ScrollView>
